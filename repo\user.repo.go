package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gorm.io/gorm/clause"
)

type UserOption func(repository.IRepository[models.User])

var User = func(c core.IContext, options ...UserOption) repository.IRepository[models.User] {
	r := repository.New[models.User](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func UserOrderBy(pageOptions *core.PageOptions) UserOption {
	return func(c repository.IRepository[models.User]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}

	}
}

func UserWithAllRelation() UserOption {
	return func(c repository.IRepository[models.User]) {
		c.Preload(clause.Associations)
	}
}
