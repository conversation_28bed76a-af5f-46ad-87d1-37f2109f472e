package requests

import (
	core "gitlab.finema.co/finema/idin-core"
)

type CheckinPaginationRequest struct {
	core.BaseValidator
	StartDate *string `json:"start_date" query:"start_date"`
	EndDate   *string `json:"end_date" query:"end_date"`
}

func (r *CheckinPaginationRequest) Validate() core.IError {
	r.Must(r.IsDate(r.StartDate, "start_date"))
	r.Must(r.IsDate(r.EndDate, "end_date"))

	return r.Error()
}
