package views

import "gitlab.finema.co/finema/finework/finework-api/models"

type ProjectSummary struct {
	ProjectCode *string `json:"project_code"`
	ProjectName *string `json:"project_name"`
	TotalTiming float64 `json:"total_timing"`
}

type TimesheetSummaryReportView struct {
	*models.User
	TotalTiming    float64          `json:"total_timing"`
	ProjectSummary []ProjectSummary `json:"project_summary"`
}
